mod common;

use shredstream_decoder::types::{LegacyMessage, MessageHeader, V0Message, VersionedMessage};

use common::{
    assertions::*, compatibility_validators::validators::*, fixtures::*, mock_data::generators::*,
    reference_implementations::reference::*,
};

mod message_header_tests {
    use super::*;

    #[test]
    fn test_message_header_basic_structure() {
        let header = MessageHeader {
            num_required_signatures: 1,
            num_readonly_signed_accounts: 2,
            num_readonly_unsigned_accounts: 3,
        };

        assert_eq!(header.num_required_signatures, 1);
        assert_eq!(header.num_readonly_signed_accounts, 2);
        assert_eq!(header.num_readonly_unsigned_accounts, 3);
    }

    #[test]
    fn test_message_header_default() {
        let header = MessageHeader::default();

        assert_eq!(header.num_required_signatures, 0);
        assert_eq!(header.num_readonly_signed_accounts, 0);
        assert_eq!(header.num_readonly_unsigned_accounts, 0);
    }

    #[test]
    fn test_message_header_clone_and_equality() {
        let header1 = MessageHeader {
            num_required_signatures: 5,
            num_readonly_signed_accounts: 10,
            num_readonly_unsigned_accounts: 15,
        };

        let header2 = header1.clone();
        assert_eq!(header1, header2);

        let header3 = MessageHeader {
            num_required_signatures: 5,
            num_readonly_signed_accounts: 10,
            num_readonly_unsigned_accounts: 16, // Different value
        };
        assert_ne!(header1, header3);
    }

    #[test]
    fn test_message_header_serialization_roundtrip() {
        let original = MessageHeader {
            num_required_signatures: 42,
            num_readonly_signed_accounts: 84,
            num_readonly_unsigned_accounts: 126,
        };

        let serialized = bincode::serialize(&original).unwrap();
        let deserialized: MessageHeader = bincode::deserialize(&serialized).unwrap();

        assert_eq!(original, deserialized);
    }

    #[test]
    fn test_message_header_edge_cases() {
        // Test with maximum u8 values
        let max_header = MessageHeader {
            num_required_signatures: u8::MAX,
            num_readonly_signed_accounts: u8::MAX,
            num_readonly_unsigned_accounts: u8::MAX,
        };

        let serialized = bincode::serialize(&max_header).unwrap();
        let deserialized: MessageHeader = bincode::deserialize(&serialized).unwrap();
        assert_eq!(max_header, deserialized);

        // Test with zero values
        let zero_header = MessageHeader {
            num_required_signatures: 0,
            num_readonly_signed_accounts: 0,
            num_readonly_unsigned_accounts: 0,
        };

        let serialized = bincode::serialize(&zero_header).unwrap();
        let deserialized: MessageHeader = bincode::deserialize(&serialized).unwrap();
        assert_eq!(zero_header, deserialized);
    }

    #[test]
    fn test_message_header_with_mock_generators() {
        // Test with mock data generators
        let mock_legacy = generate_mock_legacy_message();
        let header = &mock_legacy.header;

        // Verify header is valid
        assert!(header.num_required_signatures <= u8::MAX);
        assert!(header.num_readonly_signed_accounts <= u8::MAX);
        assert!(header.num_readonly_unsigned_accounts <= u8::MAX);

        // Test serialization of mock data
        let serialized = bincode::serialize(header).unwrap();
        let deserialized: MessageHeader = bincode::deserialize(&serialized).unwrap();
        assert_eq!(*header, deserialized);
    }

    #[test]
    fn test_message_header_binary_layout() {
        // Test that MessageHeader has expected binary layout (3 bytes)
        let header = MessageHeader {
            num_required_signatures: 1,
            num_readonly_signed_accounts: 2,
            num_readonly_unsigned_accounts: 3,
        };

        let serialized = bincode::serialize(&header).unwrap();

        // MessageHeader should serialize to exactly 3 bytes (3 u8 fields)
        assert_eq!(serialized.len(), 3);
        assert_eq!(serialized[0], 1); // num_required_signatures
        assert_eq!(serialized[1], 2); // num_readonly_signed_accounts
        assert_eq!(serialized[2], 3); // num_readonly_unsigned_accounts
    }

    #[test]
    fn test_message_header_signature_counts() {
        // Test various signature count scenarios
        let scenarios = vec![
            (0, 0, 0),       // No signatures
            (1, 0, 0),       // Single signature
            (5, 2, 3),       // Multiple signatures with readonly accounts
            (255, 128, 127), // High values
        ];

        for (req_sigs, readonly_signed, readonly_unsigned) in scenarios {
            let header = MessageHeader {
                num_required_signatures: req_sigs,
                num_readonly_signed_accounts: readonly_signed,
                num_readonly_unsigned_accounts: readonly_unsigned,
            };

            // Test serialization roundtrip
            let serialized = bincode::serialize(&header).unwrap();
            let deserialized: MessageHeader = bincode::deserialize(&serialized).unwrap();
            assert_eq!(header, deserialized);

            // Verify field values
            assert_eq!(header.num_required_signatures, req_sigs);
            assert_eq!(header.num_readonly_signed_accounts, readonly_signed);
            assert_eq!(header.num_readonly_unsigned_accounts, readonly_unsigned);
        }
    }

    #[test]
    fn test_message_header_readonly_accounts() {
        // Test readonly account counting logic
        let header = MessageHeader {
            num_required_signatures: 3,
            num_readonly_signed_accounts: 1,
            num_readonly_unsigned_accounts: 2,
        };

        // Total readonly accounts = readonly_signed + readonly_unsigned
        let total_readonly = header.num_readonly_signed_accounts + header.num_readonly_unsigned_accounts;
        assert_eq!(total_readonly, 3);

        // Verify that readonly_signed <= num_required_signatures (logical constraint)
        assert!(header.num_readonly_signed_accounts <= header.num_required_signatures);
    }

    #[test]
    fn test_message_header_comprehensive_roundtrip() {
        // Test with various combinations to ensure comprehensive coverage
        for req_sigs in [0, 1, 5, 10, 255] {
            for readonly_signed in [0, 1, req_sigs.min(5)] {
                for readonly_unsigned in [0, 1, 5, 10, 255] {
                    let header = MessageHeader {
                        num_required_signatures: req_sigs,
                        num_readonly_signed_accounts: readonly_signed,
                        num_readonly_unsigned_accounts: readonly_unsigned,
                    };

                    // Test serialization roundtrip
                    let serialized = bincode::serialize(&header).unwrap();
                    let deserialized: MessageHeader = bincode::deserialize(&serialized).unwrap();
                    assert_eq!(header, deserialized);

                    // Verify binary layout consistency
                    assert_eq!(serialized.len(), 3);
                    assert_eq!(serialized[0], req_sigs);
                    assert_eq!(serialized[1], readonly_signed);
                    assert_eq!(serialized[2], readonly_unsigned);
                }
            }
        }
    }
}

mod legacy_message_tests {
    use super::*;

    #[test]
    fn test_legacy_message_placeholder() {
        // TODO: Implement LegacyMessage tests
        // This is a Level 2 struct depending on MessageHeader, Pubkey, CompiledInstruction
        assert!(true);
    }
}

mod v0_message_tests {
    use super::*;

    #[test]
    fn test_v0_message_placeholder() {
        // TODO: Implement V0Message tests
        // This is a Level 2 struct depending on MessageHeader, Pubkey, CompiledInstruction, MessageAddressTableLookup
        assert!(true);
    }
}

mod versioned_message_tests {
    use super::*;

    #[test]
    fn test_versioned_message_placeholder() {
        // TODO: Implement VersionedMessage tests
        // This is a Level 3 struct depending on LegacyMessage, V0Message
        assert!(true);
    }
}
